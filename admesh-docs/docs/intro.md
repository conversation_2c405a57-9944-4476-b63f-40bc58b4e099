---
sidebar_position: 1
slug: /
title: AdMesh
description: Professional AI agent integration platform for intelligent product recommendations
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# AdMesh

<div className="hero">
  <div className="hero__content">
    <h1 className="hero__title">
      AdMesh
    </h1>
    <p className="hero__subtitle">
      Professional AI agent integration platform that transforms user queries into intelligent product recommendations. Seamlessly monetize your AI applications with our developer-first SDKs and smart algorithms.
    </p>
    <div style={{display: 'flex', gap: '0.5rem', justifyContent: 'center', flexWrap: 'wrap', marginTop: '1.5rem'}}>
      <a href="/getting-started/overview" className="button button--primary">
        Get Started
      </a>
      <a href="https://storybook.useadmesh.com" target="_blank" className="button button--secondary">
        Live Examples
      </a>
      <a href="https://useadmesh.com/agents#earnings-calculator" target="_blank" className="button button--outline">
        Calculate Earnings
      </a>
    </div>
  </div>
</div>

## Why Choose AdMesh?

<div className="content-grid content-grid--3">
  <div className="card">
    <div className="card__header">
      <h3 className="card__title">AI-Powered</h3>
    </div>
    <div className="card__body">
      Smart algorithms deliver relevant product recommendations with precision scoring.
    </div>
  </div>

  <div className="card">
    <div className="card__header">
      <h3 className="card__title">Developer Ready</h3>
    </div>
    <div className="card__body">
      Production SDKs with TypeScript support and seamless integration.
    </div>
  </div>

  <div className="card">
    <div className="card__header">
      <h3 className="card__title">Revenue Focused</h3>
    </div>
    <div className="card__body">
      Built-in analytics and optimization tools to maximize earnings.
    </div>
  </div>
</div>

## Choose Your SDK

<Tabs>
<TabItem value="python" label="Python SDK" default>

**Perfect for:** AI applications, data processing, server-side implementations

```python
from admesh import Admesh

# Initialize client
client = Admesh(api_key="your-api-key")

# Get intelligent recommendations
response = client.recommend.get_recommendations(
    query="Enterprise CRM solutions for distributed teams",
    format="auto",
    limit=5
)

# Access recommendations
for rec in response.recommendations:
    print(f"{rec.title}: {rec.reason}")
```

<div style={{marginTop: '1rem'}}>
  <a href="/python-sdk/installation" className="button button--primary">
    Python Documentation
  </a>
  <a href="https://github.com/GouniManikumar12/admesh-python" target="_blank" className="button button--secondary" style={{marginLeft: '0.5rem'}}>
    GitHub Repository
  </a>
</div>

</TabItem>
<TabItem value="typescript" label="TypeScript SDK">

**Perfect for:** Node.js backends, serverless functions, API services

```typescript
import Admesh from 'admesh';

// Initialize with full type safety
const client = new Admesh({
  apiKey: 'your-api-key',
  environment: 'production'
});

// Get recommendations with TypeScript support
const response = await client.recommend.getRecommendations({
  query: 'Enterprise CRM solutions for distributed teams',
  format: 'auto',
  limit: 5
});

// Fully typed response
response.recommendations.forEach(rec => {
  console.log(`${rec.title}: ${rec.reason}`);
});
```

<div style={{marginTop: '1rem'}}>
  <a href="/typescript-sdk/installation" className="button button--primary">
    TypeScript Documentation
  </a>
  <a href="https://github.com/GouniManikumar12/admesh-typescript" target="_blank" className="button button--secondary" style={{marginLeft: '0.5rem'}}>
    GitHub Repository
  </a>
</div>

</TabItem>
<TabItem value="ui" label="UI SDK">

**Perfect for:** React applications, frontend components, user interfaces

```tsx
import { AdMeshLayout, AdMeshProductCard } from 'admesh-ui-sdk';

function MyApp() {
  return (
    <div>
      {/* Intelligent layout with auto-recommendations */}
      <AdMeshLayout
        apiKey="your-api-key"
        query="Enterprise CRM solutions"
        autoLayout={true}
        theme="modern"
      />

      {/* Individual product cards */}
      <AdMeshProductCard
        productId="crm-solution-1"
        showPricing={true}
        showFeatures={true}
      />
    </div>
  );
}
```

<div style={{marginTop: '1rem'}}>
  <a href="/ui-sdk/installation" className="button button--primary">
    UI SDK Documentation
  </a>
  <a href="https://storybook.useadmesh.com" target="_blank" className="button button--secondary" style={{marginLeft: '0.5rem'}}>
    Live Examples
  </a>
</div>

</TabItem>
</Tabs>

## Quick Start Guide

<div className="content-grid content-grid--2">
  <div className="feature-box feature-box--primary">
    <h3>1. Get Your API Key</h3>
    <p>Sign up for a free AdMesh account and get your API credentials instantly.</p>
    <a href="https://useadmesh.com/agent" target="_blank" className="button button--primary">
      Get API Key
    </a>
  </div>

  <div className="feature-box feature-box--success">
    <h3>2. Install SDK</h3>
    <p>Choose your preferred SDK and install it with your package manager.</p>
    <a href="/getting-started/overview" className="button button--primary">
      Installation Guide
    </a>
  </div>
</div>

## Key Features

<div className="content-grid">
  <div className="card">
    <div className="card__header">
      <h3 className="card__title">Smart AI Engine</h3>
    </div>
    <div className="card__body">
      <ul>
        <li>Machine learning-powered intent detection</li>
        <li>Semantic matching algorithms</li>
        <li>Trust score-based filtering</li>
        <li>Real-time processing</li>
      </ul>
    </div>
  </div>

  <div className="card">
    <div className="card__header">
      <h3 className="card__title">Analytics & Tracking</h3>
    </div>
    <div className="card__body">
      <ul>
        <li>Automated interaction tracking</li>
        <li>Conversion monitoring</li>
        <li>Performance metrics</li>
        <li>Revenue analytics</li>
      </ul>
    </div>
  </div>

  <div className="card">
    <div className="card__header">
      <h3 className="card__title">UI Components</h3>
    </div>
    <div className="card__body">
      <ul>
        <li>Production-ready React components</li>
        <li>Citation-based interfaces</li>
        <li>Customizable themes</li>
        <li>Mobile-responsive design</li>
      </ul>
    </div>
  </div>

  <div className="card">
    <div className="card__header">
      <h3 className="card__title">Developer Experience</h3>
    </div>
    <div className="card__body">
      <ul>
        <li>Full TypeScript support</li>
        <li>Comprehensive error handling</li>
        <li>Async/await support</li>
        <li>Complete documentation</li>
      </ul>
    </div>
  </div>
</div>

## Documentation Navigation

<div className="content-grid content-grid--2">
  <div className="card">
    <div className="card__header">
      <h3 className="card__title">Getting Started</h3>
    </div>
    <div className="card__body">
      <p>Complete setup guide from API keys to first implementation</p>
      <a href="/getting-started/overview" className="button button--primary">
        Start Building
      </a>
    </div>
  </div>

  <div className="card">
    <div className="card__header">
      <h3 className="card__title">Ad Units</h3>
    </div>
    <div className="card__body">
      <p>Explore all available ad formats and UI components</p>
      <a href="/ad-units/overview" className="button button--primary">
        View Components
      </a>
    </div>
  </div>

  <div className="card">
    <div className="card__header">
      <h3 className="card__title">API Reference</h3>
    </div>
    <div className="card__body">
      <p>Complete API documentation with examples and schemas</p>
      <a href="/api/authentication" className="button button--primary">
        API Documentation
      </a>
    </div>
  </div>

  <div className="card">
    <div className="card__header">
      <h3 className="card__title">Examples</h3>
    </div>
    <div className="card__body">
      <p>Real-world implementation examples and tutorials</p>
      <a href="/examples/ai-assistant" className="button button--primary">
        View Examples
      </a>
    </div>
  </div>
</div>

## Earnings Calculator

<div className="earnings-calculator-box">
  <h3>Calculate Your Potential Revenue</h3>
  <p>See how much you can earn by integrating AdMesh into your AI application</p>
  <a href="https://useadmesh.com/agents#earnings-calculator" target="_blank" className="earnings-calculator-link">
    Calculate Earnings
  </a>
</div>

## Support & Community

<div className="content-grid content-grid--2">
  <div className="feature-box feature-box--success">
    <h3>Direct Support</h3>
    <p>Get help directly from our team for technical questions and integration support.</p>
    <a href="mailto:<EMAIL>" className="button button--primary">
      Contact Support
    </a>
  </div>

  <div className="feature-box feature-box--primary">
    <h3>Dashboard</h3>
    <p>Manage your API keys, view analytics, and monitor your integration performance.</p>
    <a href="https://useadmesh.com" target="_blank" className="button button--primary">
      Open Dashboard
    </a>
  </div>
</div>

---

<div style={{textAlign: 'center', padding: '2rem 0'}}>
  <h2>Ready to get started?</h2>
  <p style={{fontSize: '1.125rem', color: 'var(--ifm-color-content-secondary)', marginBottom: '2rem'}}>
    Join thousands of developers building intelligent AI applications with AdMesh
  </p>
  <a href="/getting-started/overview" className="button button--primary button--lg">
    Start Building Now
  </a>
</div>
