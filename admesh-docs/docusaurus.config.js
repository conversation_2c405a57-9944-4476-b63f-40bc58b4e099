// @ts-check
// `@type` JSDoc annotations allow editor autocompletion and type checking
// (when paired with `@ts-check`).
// There are various equivalent ways to declare your Docusaurus config.
// See: https://docusaurus.io/docs/api/docusaurus-config

const {themes: prismThemes} = require('prism-react-renderer');

/** @type {import('@docusaurus/types').Config} */
const config = {
  title: 'AdMesh SDK Documentation',
  tagline: 'AI Agent Integration Guide for Product Recommendations',
  favicon: 'img/favicon.ico',

  // Set the production url of your site here
  url: 'https://docs.useadmesh.com',
  // Set the /<baseUrl>/ pathname under which your site is served
  baseUrl: '/',

  // Vercel deployment config
  organizationName: 'GouniManikumar12',
  projectName: 'admesh-docs',

  onBrokenLinks: 'throw',
  onBrokenMarkdownLinks: 'warn',

  // Even if you don't use internationalization, you can use this field to set
  // useful metadata like html lang. For example, if your site is Chinese, you
  // may want to replace "en" with "zh-Hans".
  i18n: {
    defaultLocale: 'en',
    locales: ['en'],
  },

  markdown: {
    mermaid: true,
  },

  themes: ['@docusaurus/theme-mermaid'],

  presets: [
    [
      'classic',
      /** @type {import('@docusaurus/preset-classic').Options} */
      ({
        docs: {
          routeBasePath: '/', // Serve docs at the root
          sidebarPath: './sidebars.js',
          // Please change this to your repo.
          // Remove this to remove the "edit this page" links.
          editUrl:
            'https://github.com/GouniManikumar12/admesh-docs/tree/main/docs/',
        },
        blog: false, // Disable blog
        theme: {
          customCss: './src/css/custom.css',
        },
      }),
    ],
  ],

  themeConfig:
    /** @type {import('@docusaurus/preset-classic').ThemeConfig} */
    ({
      // Replace with your project's social card
      image: 'img/admesh-social-card.jpg',

      // Enhanced metadata
      metadata: [
        {name: 'keywords', content: 'AdMesh, AI, SDK, product recommendations, agent integration, API'},
        {name: 'description', content: 'Professional AI agent integration platform for product recommendations'},
        {property: 'og:type', content: 'website'},
        {property: 'og:site_name', content: 'AdMesh Documentation'},
      ],

      // Color mode configuration
      colorMode: {
        defaultMode: 'light',
        disableSwitch: false,
        respectPrefersColorScheme: true,
      },

      navbar: {
        title: 'AdMesh',
        hideOnScroll: false,
        style: 'primary',
        // logo: {
        //   alt: 'AdMesh Logo',
        //   src: 'img/logo.svg',
        //   srcDark: 'img/logo-dark.svg',
        //   width: 32,
        //   height: 32,
        // },
        items: [
          {
            href: 'https://storybook.useadmesh.com',
            label: 'Live Examples',
            position: 'left',
            target: '_blank',
          },
          {
            type: 'search',
            position: 'left',
          },
          {
            href: 'https://useadmesh.com/agents#earnings-calculator',
            label: 'Earnings Calculator',
            position: 'right',
            target: '_blank',
            className: 'navbar__link--earnings',
          },
          {
            href: 'https://useadmesh.com',
            label: 'Dashboard',
            position: 'right',
          },
          {
            type: 'dropdown',
            label: 'SDKs',
            position: 'right',
            items: [
              {
                href: 'https://github.com/GouniManikumar12/admesh-python',
                label: 'Python SDK',
                target: '_blank',
              },
              {
                href: 'https://github.com/GouniManikumar12/admesh-typescript',
                label: 'TypeScript SDK',
                target: '_blank',
              },
              {
                href: 'https://github.com/GouniManikumar12/admesh-ui-sdk',
                label: 'UI SDK',
                target: '_blank',
              },
            ],
          },
          {
            type: 'html',
            position: 'right',
            value: '<a href="https://github.com/GouniManikumar12" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link" aria-label="GitHub"><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/></svg></a>',
          },
        ],
      },
      footer: {
        style: 'light',
        links: [
          {
            title: 'Documentation',
            items: [
              {
                label: 'Getting Started',
                to: '/getting-started/overview',
              },
              {
                label: 'Ad Units',
                to: '/ad-units/overview',
              },
              {
                label: 'API Reference',
                to: '/api/authentication',
              },
              {
                label: 'Examples',
                to: '/examples/ai-assistant',
              },
            ],
          },
          {
            title: 'SDKs & Tools',
            items: [
              {
                label: 'Python SDK',
                href: 'https://github.com/GouniManikumar12/admesh-python',
                target: '_blank',
              },
              {
                label: 'TypeScript SDK',
                href: 'https://github.com/GouniManikumar12/admesh-typescript',
                target: '_blank',
              },
              {
                label: 'UI SDK',
                href: 'https://github.com/GouniManikumar12/admesh-ui-sdk',
                target: '_blank',
              },
              {
                label: 'Live Examples',
                href: 'https://storybook.useadmesh.com',
                target: '_blank',
              },
            ],
          },
          {
            title: 'Platform',
            items: [
              {
                label: 'AdMesh Dashboard',
                href: 'https://useadmesh.com',
                target: '_blank',
              },
              {
                label: 'Earnings Calculator',
                href: 'https://useadmesh.com/agents#earnings-calculator',
                target: '_blank',
              },
              {
                label: 'Agent Signup',
                href: 'https://useadmesh.com/agent',
                target: '_blank',
              },
              {
                label: 'Brand Signup',
                href: 'https://useadmesh.com/brand',
                target: '_blank',
              },
            ],
          },
          {
            title: 'Community & Support',
            items: [
              {
                label: 'GitHub',
                href: 'https://github.com/GouniManikumar12',
                target: '_blank',
              },
              {
                label: 'Support',
                href: 'mailto:<EMAIL>',
              },
              {
                label: 'LinkedIn',
                href: 'https://www.linkedin.com/in/manikumargouni',
                target: '_blank',
              },
              {
                label: 'Status',
                href: 'https://status.useadmesh.com',
                target: '_blank',
              },
            ],
          },
        ],
        logo: {
          alt: 'AdMesh Logo',
          src: 'img/logo.svg',
          width: 160,
          height: 51,
        },
        copyright: `
          <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid var(--ifm-border-color);">
            <p>Copyright © ${new Date().getFullYear()} AdMesh. All rights reserved.</p>
          </div>
        `,
      },
      prism: {
        theme: prismThemes.github,
        darkTheme: prismThemes.dracula,
        additionalLanguages: ['bash', 'json', 'python', 'typescript', 'javascript'],
      },
      algolia: {
        // The application ID provided by Algolia
        appId: 'YOUR_APP_ID',
        // Public API key: it is safe to commit it
        apiKey: 'YOUR_SEARCH_API_KEY',
        indexName: 'admesh-docs',
        // Optional: see doc section below
        contextualSearch: true,
        // Optional: Specify domains where the navigation should occur through window.location instead on history.push
        externalUrlRegex: 'external\\.com|domain\\.com',
        // Optional: Replace parts of the item URLs from Algolia. Useful when using the same search index for multiple deployments using a different baseUrl.
        replaceSearchResultPathname: {
          from: '/docs/', // or as RegExp: /\/docs\//
          to: '/',
        },
        // Optional: Algolia search parameters
        searchParameters: {},
        // Optional: path for search page that enabled by default (`false` to disable it)
        searchPagePath: 'search',
      },
    }),
};

module.exports = config;
