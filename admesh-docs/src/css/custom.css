/**
 * AdMesh Documentation - Modern Design System
 * Inspired by <PERSON>e, Vercel, and Linear documentation
 */

/* Import Inter font for modern typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Modern Design System Variables - Black & White Minimal */
:root {
  /* Brand Colors - Professional Black & White */
  --ifm-color-primary: #000000;
  --ifm-color-primary-dark: #000000;
  --ifm-color-primary-darker: #000000;
  --ifm-color-primary-darkest: #000000;
  --ifm-color-primary-light: #333333;
  --ifm-color-primary-lighter: #666666;
  --ifm-color-primary-lightest: #999999;

  /* Semantic Colors - Minimal <PERSON>scale */
  --ifm-color-success: #000000;
  --ifm-color-warning: #333333;
  --ifm-color-danger: #000000;
  --ifm-color-info: #000000;

  /* Typography */
  --ifm-font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --ifm-font-family-monospace: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  --ifm-font-size-base: 16px;
  --ifm-line-height-base: 1.6;
  --ifm-font-weight-light: 300;
  --ifm-font-weight-normal: 400;
  --ifm-font-weight-semibold: 500;
  --ifm-font-weight-bold: 600;

  /* Spacing */
  --ifm-spacing-horizontal: 1.5rem;
  --ifm-spacing-vertical: 1.5rem;
  --ifm-container-width: 1200px;
  --ifm-container-width-xl: 1400px;

  /* Borders and Radius */
  --ifm-border-radius: 8px;
  --ifm-border-width: 1px;
  --ifm-border-color: #e5e5e5;

  /* Shadows - Minimal */
  --ifm-box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --ifm-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --ifm-box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --ifm-box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Code */
  --ifm-code-font-size: 0.875rem;
  --ifm-code-background: #f5f5f5;
  --ifm-code-border-radius: 6px;
  --ifm-code-padding-horizontal: 0.5rem;
  --ifm-code-padding-vertical: 0.25rem;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.05);

  /* Background Colors - Black & White */
  --ifm-background-color: #ffffff;
  --ifm-background-surface-color: #fafafa;
  --ifm-color-emphasis-0: #fafafa;
  --ifm-color-emphasis-100: #f5f5f5;
  --ifm-color-emphasis-200: #e5e5e5;
  --ifm-color-emphasis-300: #d4d4d4;

  /* Text Colors - Black & White */
  --ifm-color-content: #000000;
  --ifm-color-content-secondary: #666666;
  --ifm-heading-color: #000000;
}

/* Dark Mode Variables - Black & White */
[data-theme='dark'] {
  /* Brand Colors - Black & White for dark mode */
  --ifm-color-primary: #ffffff;
  --ifm-color-primary-dark: #e5e5e5;
  --ifm-color-primary-darker: #d4d4d4;
  --ifm-color-primary-darkest: #a3a3a3;
  --ifm-color-primary-light: #ffffff;
  --ifm-color-primary-lighter: #ffffff;
  --ifm-color-primary-lightest: #ffffff;

  /* Dark Mode Backgrounds */
  --ifm-background-color: #000000;
  --ifm-background-surface-color: #1a1a1a;
  --ifm-color-emphasis-0: #1a1a1a;
  --ifm-color-emphasis-100: #333333;
  --ifm-color-emphasis-200: #4d4d4d;
  --ifm-color-emphasis-300: #666666;

  /* Dark Mode Text */
  --ifm-color-content: #ffffff;
  --ifm-color-content-secondary: #a3a3a3;
  --ifm-heading-color: #ffffff;

  /* Dark Mode Code */
  --ifm-code-background: #1a1a1a;
  --ifm-border-color: #333333;
  --docusaurus-highlighted-code-line-bg: rgba(255, 255, 255, 0.05);
}

/* ===== MODERN LAYOUT COMPONENTS ===== */

/* Clean Minimal Navbar */
.navbar {
  background: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  box-shadow: none;
  padding: 1.25rem 0;
  min-height: 70px;
}

[data-theme='dark'] .navbar {
  background: #000000;
  border-bottom-color: #333333;
}

.navbar__brand {
  font-weight: 600;
  font-size: 1.125rem;
  color: #000000;
  transition: opacity 0.2s ease;
}

.navbar__brand:hover {
  color: #000000;
  opacity: 0.7;
  text-decoration: none;
}

[data-theme='dark'] .navbar__brand {
  color: #ffffff;
}

[data-theme='dark'] .navbar__brand:hover {
  color: #ffffff;
}

.navbar__item {
  font-weight: 400;
  font-size: 0.9rem;
}

.navbar__link {
  color: #666666;
  padding: 0.75rem 1rem !important;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.navbar__link:hover {
  background: #f5f5f5;
  color: #000000;
}

[data-theme='dark'] .navbar__link {
  color: #a3a3a3;
}

[data-theme='dark'] .navbar__link:hover {
  background: #1a1a1a;
  color: #ffffff;
}

/* ===== CLEAN MINIMAL SIDEBAR ===== */

/* Sidebar Container */
.theme-doc-sidebar-container {
  background: #fafafa;
  border-right: 1px solid #e5e5e5;
}

[data-theme='dark'] .theme-doc-sidebar-container {
  background: #0a0a0a;
  border-right-color: #333333;
}

/* Sidebar Menu */
.menu {
  background: transparent;
  padding: 1.5rem 0;
}

.menu__list {
  margin: 0;
  padding: 0;
}

.menu__list-item {
  margin: 0;
}

.menu__link {
  color: #666666;
  padding: 0.375rem 1rem;
  border-radius: 0;
  margin: 0;
  transition: all 0.2s ease;
  font-weight: 400;
  font-size: 0.875rem;
  border-left: 3px solid transparent;
  border: none;
}

.menu__link:hover {
  background: #f0f0f0;
  color: #000000;
  text-decoration: none;
  border-left-color: #e5e5e5;
}

.menu__link--active {
  background: #ffffff;
  color: #000000;
  font-weight: 500;
  border-left-color: #000000;
}

.menu__link--active:hover {
  background: #ffffff;
  color: #000000;
}

[data-theme='dark'] .menu__link {
  color: #a3a3a3;
}

[data-theme='dark'] .menu__link:hover {
  background: #1a1a1a;
  color: #ffffff;
  border-left-color: #333333;
}

[data-theme='dark'] .menu__link--active {
  background: #1a1a1a;
  color: #ffffff;
  border-left-color: #ffffff;
}

[data-theme='dark'] .menu__link--active:hover {
  background: #1a1a1a;
  color: #ffffff;
}

/* Sidebar Category Labels */
.menu__link--sublist {
  color: #000000;
  font-weight: 600;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.5rem 1rem;
  margin-bottom: 0.25rem;
}

.menu__link--sublist:hover {
  background: #f0f0f0;
  color: #000000;
}

[data-theme='dark'] .menu__link--sublist {
  color: #ffffff;
}

[data-theme='dark'] .menu__link--sublist:hover {
  background: #1a1a1a;
  color: #ffffff;
}

/* Nested Menu Items */
.menu__list .menu__list {
  padding-left: 0;
  margin-left: 0;
  border-left: none;
}

.menu__list .menu__list .menu__link {
  padding-left: 2rem;
  font-size: 0.8rem;
}

.menu__list .menu__list .menu__link {
  padding-left: 1.5rem;
  font-size: 0.875rem;
}

/* Dark Mode Sidebar */
[data-theme='dark'] .menu__link {
  color: var(--ifm-color-content);
}

[data-theme='dark'] .menu__link:hover {
  background: var(--ifm-color-emphasis-100);
  color: var(--ifm-color-primary);
}

[data-theme='dark'] .menu__link--active {
  background: var(--ifm-color-primary);
  color: var(--ifm-background-color);
}

[data-theme='dark'] .menu__link--sublist {
  color: var(--ifm-heading-color);
}

[data-theme='dark'] .menu__list .menu__list {
  border-left-color: var(--ifm-color-emphasis-200);
}

/* Clean Minimal Hero Section */
.hero {
  background: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  padding: 3rem 0;
  text-align: center;
}

[data-theme='dark'] .hero {
  background: #000000;
  border-bottom-color: #333333;
}

.hero__title {
  color: #000000;
  font-weight: 600;
  font-size: 2.25rem;
  line-height: 1.2;
  margin-bottom: 1rem;
}

[data-theme='dark'] .hero__title {
  color: #ffffff;
}

.hero__subtitle {
  font-size: 1rem;
  color: #666666;
  font-weight: 400;
  max-width: 500px;
  margin: 0 auto 1.5rem;
  line-height: 1.4;
}

[data-theme='dark'] .hero__subtitle {
  color: #a3a3a3;
}

/* Clean Minimal Buttons */
.button {
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.8rem;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  box-shadow: none;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.button:hover {
  transform: none;
  box-shadow: none;
  text-decoration: none;
}

.button--primary {
  background: #000000 !important;
  color: #ffffff !important;
  border-color: #000000;
}

.button--primary:hover {
  background: #333333 !important;
  border-color: #333333;
  color: #ffffff !important;
}

[data-theme='dark'] .button--primary {
  background: #ffffff !important;
  color: #000000 !important;
  border-color: #ffffff;
}

[data-theme='dark'] .button--primary:hover {
  background: #e5e5e5 !important;
  border-color: #e5e5e5;
  color: #000000 !important;
}

.button--secondary {
  background: transparent !important;
  color: #666666 !important;
  border-color: #e5e5e5;
}

.button--secondary:hover {
  background: #f5f5f5 !important;
  color: #000000 !important;
  border-color: #d4d4d4;
}

[data-theme='dark'] .button--secondary {
  color: #a3a3a3 !important;
  border-color: #333333;
}

[data-theme='dark'] .button--secondary:hover {
  background: #1a1a1a !important;
  color: #ffffff !important;
  border-color: #4d4d4d;
}

.button--outline {
  background: transparent !important;
  color: #000000 !important;
  border-color: #000000;
}

.button--outline:hover {
  background: #000000 !important;
  color: #ffffff !important;
}

[data-theme='dark'] .button--outline {
  color: #ffffff !important;
  border-color: #ffffff;
}

[data-theme='dark'] .button--outline:hover {
  background: #ffffff !important;
  color: #000000 !important;
}

/* Typography Improvements */
h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #000000;
  margin-bottom: 1rem;
  line-height: 1.3;
}

[data-theme='dark'] h2 {
  color: #ffffff;
}

h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #000000;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

[data-theme='dark'] h3 {
  color: #ffffff;
}

/* Improve spacing for intro page */
.markdown > h2:first-of-type {
  margin-top: 2rem;
}

.markdown > h2 {
  margin-top: 2.5rem;
  margin-bottom: 1.25rem;
}

/* ===== CODE BLOCKS & SYNTAX HIGHLIGHTING ===== */

/* Modern Code Blocks */
.prism-code {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  box-shadow: var(--ifm-box-shadow-sm);
  background: var(--ifm-code-background) !important;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  line-height: 1.6;
  overflow-x: auto;
}

.code-block-with-title {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--ifm-border-color);
  box-shadow: var(--ifm-box-shadow-sm);
}

.code-block-title {
  background: var(--ifm-color-emphasis-100);
  color: var(--ifm-color-content-secondary);
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--ifm-border-color);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Inline Code */
code {
  background: var(--ifm-code-background);
  color: var(--ifm-color-primary-dark);
  border: 1px solid var(--ifm-color-emphasis-200);
  border-radius: var(--ifm-code-border-radius);
  padding: var(--ifm-code-padding-vertical) var(--ifm-code-padding-horizontal);
  font-family: var(--ifm-font-family-monospace);
  font-size: var(--ifm-code-font-size);
  font-weight: 500;
}

/* Copy Button Improvements */
.clean-btn {
  transition: all 0.2s ease;
  border-radius: 6px;
  padding: 0.5rem;
  background: var(--ifm-color-emphasis-100);
  border: 1px solid var(--ifm-border-color);
}

.clean-btn:hover {
  transform: translateY(-1px);
  background: var(--ifm-color-primary);
  color: white;
  border-color: var(--ifm-color-primary);
}

/* Language Labels */
.prism-code .token-line::before {
  content: attr(data-language);
  position: absolute;
  top: 0.75rem;
  right: 1rem;
  font-size: 0.75rem;
  color: var(--ifm-color-content-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
}

/* ===== CONTENT LAYOUT & CARDS ===== */

/* Clean Minimal Cards */
.card {
  background: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  padding: 1.25rem;
  box-shadow: none;
  transition: all 0.2s ease;
  height: 100%;
}

.card:hover {
  border-color: #d4d4d4;
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

[data-theme='dark'] .card {
  background: #1a1a1a;
  border-color: #333333;
}

[data-theme='dark'] .card:hover {
  border-color: #4d4d4d;
  box-shadow: 0 1px 3px rgba(255, 255, 255, 0.1);
}

.card__header {
  margin-bottom: 0.75rem;
}

.card__title {
  font-size: 1rem;
  font-weight: 600;
  color: #000000;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

[data-theme='dark'] .card__title {
  color: #ffffff;
}

.card__body {
  color: #666666;
  line-height: 1.5;
  font-size: 0.875rem;
}

[data-theme='dark'] .card__body {
  color: #a3a3a3;
}

/* Modern Badges */
.badge {
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: none;
}

.badge--primary {
  background: var(--ifm-color-primary);
  color: white;
}

.badge--success {
  background: var(--ifm-color-success);
  color: white;
}

.badge--warning {
  background: var(--ifm-color-warning);
  color: white;
}

.badge--danger {
  background: var(--ifm-color-danger);
  color: white;
}

.badge--info {
  background: var(--ifm-color-info);
  color: white;
}

/* Clean Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
}

.content-grid--2 {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.content-grid--3 {
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 0.75rem;
}

/* ===== API DOCUMENTATION COMPONENTS ===== */

/* Modern API Endpoint Styling */
.api-endpoint {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  font-weight: 600;
  box-shadow: var(--ifm-box-shadow-sm);
  transition: all 0.2s ease;
}

.api-endpoint:hover {
  transform: translateY(-1px);
  box-shadow: var(--ifm-box-shadow-md);
}

.api-endpoint--get {
  background: var(--ifm-color-emphasis-0);
  color: var(--ifm-color-success);
  border: 1px solid var(--ifm-border-color);
}

.api-endpoint--post {
  background: var(--ifm-color-emphasis-0);
  color: var(--ifm-color-info);
  border: 1px solid var(--ifm-border-color);
}

.api-endpoint--put {
  background: var(--ifm-color-emphasis-0);
  color: var(--ifm-color-warning);
  border: 1px solid var(--ifm-border-color);
}

.api-endpoint--delete {
  background: var(--ifm-color-emphasis-0);
  color: var(--ifm-color-danger);
  border: 1px solid var(--ifm-border-color);
}

/* API Method Labels */
.api-method {
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background: currentColor;
  color: white;
  margin-right: 0.5rem;
}

/* ===== TABLES & DATA DISPLAY ===== */

/* Modern Tables */
.table-wrapper {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 1.5rem 0;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  background: var(--ifm-background-color);
}

th {
  background: var(--ifm-color-emphasis-100);
  color: var(--ifm-heading-color);
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--ifm-border-color);
  text-align: left;
}

td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--ifm-border-color);
  color: var(--ifm-color-content);
  vertical-align: top;
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background: var(--ifm-color-emphasis-0);
}

/* SDK Comparison Table */
.sdk-comparison {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--ifm-border-color);
  box-shadow: var(--ifm-box-shadow-sm);
}

.sdk-comparison th,
.sdk-comparison td {
  padding: 1rem 1.5rem;
  text-align: left;
  border-bottom: 1px solid var(--ifm-border-color);
}

.sdk-comparison th {
  background: var(--ifm-color-emphasis-100);
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--ifm-heading-color);
}

.sdk-comparison tr:hover {
  background: var(--ifm-color-emphasis-0);
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.status-indicator--available {
  color: var(--ifm-color-success);
}

.status-indicator--coming-soon {
  color: var(--ifm-color-warning);
}

.status-indicator--not-available {
  color: var(--ifm-color-content-secondary);
}

/* ===== ADMONITIONS & FEATURE BOXES ===== */

/* Modern Admonitions */
.admonition {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 1.5rem 0;
  overflow: hidden;
  background: var(--ifm-background-color);
}

.admonition-heading {
  background: var(--ifm-color-emphasis-100);
  padding: 1rem 1.5rem;
  margin: 0;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--ifm-border-color);
}

.admonition-content {
  padding: 1.5rem;
}

/* Admonition Types */
.admonition-note {
  border-left: 4px solid var(--ifm-color-info);
}

.admonition-note .admonition-heading {
  color: var(--ifm-color-info);
  background: var(--ifm-color-emphasis-100);
}

.admonition-tip {
  border-left: 4px solid var(--ifm-color-success);
}

.admonition-tip .admonition-heading {
  color: var(--ifm-color-success);
  background: var(--ifm-color-emphasis-100);
}

.admonition-warning {
  border-left: 4px solid var(--ifm-color-warning);
}

.admonition-warning .admonition-heading {
  color: var(--ifm-color-warning);
  background: var(--ifm-color-emphasis-100);
}

.admonition-danger {
  border-left: 4px solid var(--ifm-color-danger);
}

.admonition-danger .admonition-heading {
  color: var(--ifm-color-danger);
  background: var(--ifm-color-emphasis-100);
}

/* Feature Boxes */
.feature-box {
  padding: 2rem;
  border-radius: 12px;
  margin: 2rem 0;
  border: 1px solid var(--ifm-border-color);
  background: var(--ifm-background-color);
  box-shadow: var(--ifm-box-shadow-sm);
  transition: all 0.2s ease;
}

.feature-box:hover {
  box-shadow: var(--ifm-box-shadow-md);
  transform: translateY(-2px);
}

.feature-box--primary {
  border-left: 4px solid var(--ifm-color-primary);
  background: var(--ifm-color-emphasis-0);
}

.feature-box--success {
  border-left: 4px solid var(--ifm-color-success);
  background: var(--ifm-color-emphasis-0);
}

.feature-box--warning {
  border-left: 4px solid var(--ifm-color-warning);
  background: var(--ifm-color-emphasis-0);
}

.feature-box--danger {
  border-left: 4px solid var(--ifm-color-danger);
  background: var(--ifm-color-emphasis-0);
}

/* ===== INTERACTIVE COMPONENTS ===== */

/* Modern Tabs */
.tabs {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 1.5rem 0;
}

.tabs__nav {
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
  padding: 0;
  margin: 0;
}

.tabs__item {
  padding: 1rem 1.5rem;
  cursor: pointer;
  border: none;
  background: none;
  color: var(--ifm-color-content-secondary);
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.tabs__item:hover {
  background: var(--ifm-color-emphasis-200);
  color: var(--ifm-color-content);
}

.tabs__item--active {
  color: var(--ifm-color-primary);
  background: var(--ifm-background-color);
  border-bottom-color: var(--ifm-color-primary);
}

.tabs__content {
  padding: 1.5rem;
  background: var(--ifm-background-color);
}

/* Interactive Code Examples */
.code-example {
  position: relative;
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 1.5rem 0;
}

.code-example__tabs {
  display: flex;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
  margin: 0;
}

.code-example__tab {
  padding: 1rem 1.5rem;
  cursor: pointer;
  border: none;
  background: none;
  color: var(--ifm-color-content-secondary);
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.code-example__tab:hover {
  background: var(--ifm-color-emphasis-200);
  color: var(--ifm-color-content);
}

.code-example__tab--active {
  color: var(--ifm-color-primary);
  background: var(--ifm-background-color);
  border-bottom-color: var(--ifm-color-primary);
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--ifm-color-emphasis-300);
  border-radius: 50%;
  border-top-color: var(--ifm-color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-skeleton {
  background: linear-gradient(90deg, var(--ifm-color-emphasis-100) 25%, var(--ifm-color-emphasis-200) 50%, var(--ifm-color-emphasis-100) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 6px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Optimizations */
@media (max-width: 996px) {
  .hero {
    padding: 2.5rem 0;
  }

  .hero__title {
    font-size: 1.875rem;
  }

  .hero__subtitle {
    font-size: 0.9rem;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .content-grid--2,
  .content-grid--3 {
    grid-template-columns: 1fr;
  }

  .card {
    padding: 1rem;
  }

  .feature-box {
    padding: 1.5rem;
  }

  .tabs__item,
  .code-example__tab {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }

  /* Fix mobile navigation */
  .navbar__toggle {
    display: block !important;
  }

  .navbar__items--right {
    display: none;
  }

  .navbar__items--right.navbar__items--show-mobile {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--ifm-navbar-background-color);
    border-top: 1px solid var(--ifm-border-color);
    padding: 1rem;
    z-index: 1000;
  }
}

@media (max-width: 768px) {
  .hero__title {
    font-size: 1.625rem;
    line-height: 1.2;
  }

  .hero__subtitle {
    font-size: 0.875rem;
  }

  .api-endpoint {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    gap: 0.25rem;
  }

  .sdk-comparison th,
  .sdk-comparison td {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .feature-box {
    padding: 1.25rem;
  }

  .card {
    padding: 0.875rem;
  }

  .button {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
  }

  .navbar__item {
    margin: 0 0.25rem;
  }

  .tabs__item,
  .code-example__tab {
    padding: 0.625rem 0.875rem;
    font-size: 0.75rem;
  }

  th, td {
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 1.5rem 0;
  }

  .hero__title {
    font-size: 1.5rem;
  }

  .hero__subtitle {
    font-size: 0.8rem;
  }

  .card,
  .feature-box {
    padding: 1rem;
  }

  .api-endpoint {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }

  .button {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
}

/* ===== DARK MODE ENHANCEMENTS ===== */

[data-theme='dark'] .hero {
  background: #000000;
  border-bottom-color: #333333;
}

[data-theme='dark'] .card:hover {
  border-color: var(--ifm-color-primary);
}

[data-theme='dark'] .feature-box--primary {
  background: var(--ifm-color-emphasis-0);
}

[data-theme='dark'] .feature-box--success {
  background: var(--ifm-color-emphasis-0);
}

[data-theme='dark'] .feature-box--warning {
  background: var(--ifm-color-emphasis-0);
}

[data-theme='dark'] .feature-box--danger {
  background: var(--ifm-color-emphasis-0);
}

[data-theme='dark'] .admonition-note .admonition-heading {
  background: var(--ifm-color-emphasis-100);
}

[data-theme='dark'] .admonition-tip .admonition-heading {
  background: var(--ifm-color-emphasis-100);
}

[data-theme='dark'] .admonition-warning .admonition-heading {
  background: var(--ifm-color-emphasis-100);
}

[data-theme='dark'] .admonition-danger .admonition-heading {
  background: var(--ifm-color-emphasis-100);
}

/* ===== MODERN UTILITIES ===== */

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus States */
*:focus {
  outline: 2px solid var(--ifm-color-primary);
  outline-offset: 2px;
}

/* Selection */
::selection {
  background: var(--ifm-color-primary);
  color: white;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--ifm-color-emphasis-100);
}

::-webkit-scrollbar-thumb {
  background: var(--ifm-color-emphasis-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--ifm-color-primary);
}

/* Modern Link Styling */
a {
  color: var(--ifm-color-primary);
  text-decoration: none;
  transition: all 0.2s ease;
}

a:hover {
  color: var(--ifm-color-primary-dark);
  text-decoration: underline;
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
}

/* Improved Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  color: var(--ifm-heading-color);
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  font-size: 2rem;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
}

h3 {
  font-size: 1.5rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

p {
  line-height: 1.7;
  color: var(--ifm-color-content);
}

/* Modern List Styling */
ul, ol {
  line-height: 1.7;
}

li {
  margin-bottom: 0.5rem;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== EARNINGS CALCULATOR COMPONENTS ===== */

/* Modern Earnings Calculator Link */
.navbar__link--earnings {
  background: var(--ifm-color-primary) !important;
  color: white !important;
  padding: 0.5rem 1rem !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
  margin-left: 0.5rem !important;
  box-shadow: var(--ifm-box-shadow-sm) !important;
}

.navbar__link--earnings:hover {
  transform: translateY(-1px) !important;
  box-shadow: var(--ifm-box-shadow-md) !important;
  color: white !important;
  background: var(--ifm-color-primary-light) !important;
}

[data-theme='dark'] .navbar__link--earnings {
  background: var(--ifm-color-primary) !important;
}

/* Earnings Calculator Button Component */
.earnings-calculator-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--ifm-color-primary);
  color: white;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  box-shadow: var(--ifm-box-shadow-md);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.earnings-calculator-link:hover {
  transform: translateY(-2px);
  box-shadow: var(--ifm-box-shadow-lg);
  color: white;
  text-decoration: none;
  background: var(--ifm-color-primary-light);
}

/* Earnings Calculator Feature Box */
.earnings-calculator-box {
  background: var(--ifm-color-emphasis-0);
  border: 2px solid var(--ifm-color-primary);
  border-radius: 16px;
  padding: 2rem;
  margin: 2rem 0;
  text-align: center;
  box-shadow: var(--ifm-box-shadow-md);
  transition: all 0.2s ease;
}

.earnings-calculator-box:hover {
  transform: translateY(-2px);
  box-shadow: var(--ifm-box-shadow-lg);
}

.earnings-calculator-box h3 {
  color: var(--ifm-heading-color);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.earnings-calculator-box p {
  color: var(--ifm-color-content-secondary);
  margin-bottom: 1.5rem;
  font-size: 1.125rem;
}

[data-theme='dark'] .earnings-calculator-box {
  background: var(--ifm-color-emphasis-0);
  border-color: var(--ifm-color-primary);
}

/* ===== FINAL MODERN TOUCHES ===== */

/* Smooth Animations */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

/* Modern Footer - Black & White Theme */
.footer {
  background: var(--ifm-background-color);
  border-top: 1px solid var(--ifm-border-color);
  padding: 3rem 0 2rem;
  color: var(--ifm-color-content);
}

[data-theme='dark'] .footer {
  background: var(--ifm-background-color);
  border-top-color: var(--ifm-border-color);
  color: var(--ifm-color-content);
}

.footer__title {
  font-weight: 600;
  color: var(--ifm-heading-color);
  margin-bottom: 1rem;
}

.footer__item {
  color: var(--ifm-color-content-secondary);
  transition: all 0.2s ease;
  text-decoration: none;
}

.footer__item:hover {
  color: var(--ifm-color-primary);
  text-decoration: none;
}

.footer__link-item {
  color: var(--ifm-color-content-secondary);
  transition: all 0.2s ease;
}

.footer__link-item:hover {
  color: var(--ifm-color-primary);
  text-decoration: none;
}

.footer__copyright {
  color: var(--ifm-color-content-secondary);
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--ifm-border-color);
  text-align: center;
}

[data-theme='dark'] .footer__title {
  color: var(--ifm-heading-color);
}

[data-theme='dark'] .footer__item {
  color: var(--ifm-color-content-secondary);
}

[data-theme='dark'] .footer__item:hover {
  color: var(--ifm-color-primary);
}

[data-theme='dark'] .footer__link-item {
  color: var(--ifm-color-content-secondary);
}

[data-theme='dark'] .footer__link-item:hover {
  color: var(--ifm-color-primary);
}

[data-theme='dark'] .footer__copyright {
  color: var(--ifm-color-content-secondary);
  border-top-color: var(--ifm-border-color);
}

/* Ensure no navigation elements appear in footer */
.footer .navbar__toggle,
.footer .navbar__items {
  display: none !important;
}

/* Modern Search */
.navbar__search {
  margin-right: 1rem;
}

.DocSearch-Button {
  border-radius: 8px !important;
  border: 1px solid var(--ifm-border-color) !important;
  background: var(--ifm-background-surface-color) !important;
  transition: all 0.2s ease !important;
}

.DocSearch-Button:hover {
  border-color: var(--ifm-color-primary) !important;
  box-shadow: var(--ifm-box-shadow-sm) !important;
}

/* ===== MODERN COMPONENTS ===== */

/* Modern Code Block */
.modern-code-block {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 1.5rem 0;
}

.code-block-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--ifm-color-emphasis-100);
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--ifm-border-color);
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  font-weight: 600;
}

.code-block-title__text {
  color: var(--ifm-color-content-secondary);
}

.code-block-copy-button {
  background: none;
  border: none;
  color: var(--ifm-color-content-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-block-copy-button:hover {
  background: var(--ifm-color-emphasis-200);
  color: var(--ifm-color-primary);
}

/* Interactive Demo */
.interactive-demo {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 2rem 0;
  background: var(--ifm-background-color);
}

.interactive-demo__header {
  padding: 1.5rem;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
}

.interactive-demo__header h3 {
  margin: 0 0 0.5rem 0;
  color: var(--ifm-heading-color);
}

.interactive-demo__header p {
  margin: 0;
  color: var(--ifm-color-content-secondary);
}

.interactive-demo__tabs {
  display: flex;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
}

.interactive-demo__tab {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--ifm-color-content-secondary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.interactive-demo__tab:hover {
  background: var(--ifm-color-emphasis-200);
  color: var(--ifm-color-content);
}

.interactive-demo__tab--active {
  color: var(--ifm-color-primary);
  background: var(--ifm-background-color);
  border-bottom-color: var(--ifm-color-primary);
}

.interactive-demo__example {
  padding: 1.5rem;
}

.interactive-demo__code {
  position: relative;
  background: var(--ifm-code-background);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.interactive-demo__code pre {
  margin: 0;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  line-height: 1.6;
  overflow-x: auto;
}

.interactive-demo__run-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--ifm-color-primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.interactive-demo__run-button:hover:not(:disabled) {
  background: var(--ifm-color-primary-dark);
  transform: translateY(-1px);
}

.interactive-demo__run-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.interactive-demo__result {
  background: var(--ifm-color-emphasis-0);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--ifm-border-color);
}

.interactive-demo__result h4 {
  margin: 0 0 1rem 0;
  color: var(--ifm-heading-color);
}

.interactive-demo__success pre {
  background: var(--ifm-code-background);
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  margin: 0;
}

.interactive-demo__error {
  color: var(--ifm-color-danger);
  background: var(--ifm-color-emphasis-0);
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid var(--ifm-border-color);
}

/* Feature Comparison */
.feature-comparison {
  margin: 2rem 0;
}

.feature-comparison h3 {
  margin-bottom: 1.5rem;
  color: var(--ifm-heading-color);
}

/* ===== PROFESSIONAL COMPONENTS ===== */

/* API Reference */
.api-reference {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
  box-shadow: var(--ifm-box-shadow-sm);
  margin: 2rem 0;
  background: var(--ifm-background-color);
}

.api-reference__header {
  padding: 1.5rem;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
}

.api-reference__endpoint {
  margin-bottom: 1rem;
}

.api-reference__description {
  margin: 0;
  color: var(--ifm-color-content-secondary);
  line-height: 1.6;
}

.api-reference__tabs {
  display: flex;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
}

.api-reference__tab {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--ifm-color-content-secondary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.api-reference__tab:hover {
  background: var(--ifm-color-emphasis-200);
  color: var(--ifm-color-content);
}

.api-reference__tab--active {
  color: var(--ifm-color-primary);
  background: var(--ifm-background-color);
  border-bottom-color: var(--ifm-color-primary);
}

.api-reference__tab-content {
  padding: 1.5rem;
}

.api-response {
  margin-bottom: 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--ifm-border-color);
  overflow: hidden;
}

.api-response__header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--ifm-color-emphasis-100);
  border-bottom: 1px solid var(--ifm-border-color);
}

.api-response__description {
  color: var(--ifm-color-content-secondary);
}

.api-response__schema {
  margin: 0;
  padding: 1rem;
  background: var(--ifm-code-background);
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  overflow-x: auto;
}

.api-example {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--ifm-border-color);
  background: var(--ifm-color-emphasis-0);
}

.api-example h4 {
  margin: 0 0 0.5rem 0;
  color: var(--ifm-heading-color);
}

.api-example p {
  margin: 0 0 1rem 0;
  color: var(--ifm-color-content-secondary);
}

.api-example pre {
  background: var(--ifm-code-background);
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  font-family: var(--ifm-font-family-monospace);
  font-size: 0.875rem;
  margin: 1rem 0;
}

.api-example__response {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--ifm-border-color);
}

.api-example__response h5 {
  margin: 0 0 0.5rem 0;
  color: var(--ifm-heading-color);
}

/* Feature Callout */
.feature-callout {
  border-radius: 12px;
  border: 1px solid var(--ifm-border-color);
  padding: 1.5rem;
  margin: 1.5rem 0;
  background: var(--ifm-background-color);
  box-shadow: var(--ifm-box-shadow-sm);
  transition: all 0.2s ease;
}

.feature-callout:hover {
  box-shadow: var(--ifm-box-shadow-md);
  transform: translateY(-1px);
}

.feature-callout__header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.feature-callout__icon {
  font-size: 1.5rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--ifm-color-emphasis-100);
}

.feature-callout__title {
  margin: 0;
  color: var(--ifm-heading-color);
  font-size: 1.25rem;
  font-weight: 600;
}

.feature-callout__content {
  color: var(--ifm-color-content);
  line-height: 1.6;
}

.feature-callout__action {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--ifm-border-color);
}

/* Feature Callout Types */
.feature-callout--primary {
  border-left: 4px solid var(--ifm-color-primary);
  background: var(--ifm-color-emphasis-0);
}

.feature-callout--primary .feature-callout__icon {
  background: var(--ifm-color-emphasis-100);
  color: var(--ifm-color-primary);
}

.feature-callout--success {
  border-left: 4px solid var(--ifm-color-success);
  background: var(--ifm-color-emphasis-0);
}

.feature-callout--success .feature-callout__icon {
  background: var(--ifm-color-emphasis-100);
  color: var(--ifm-color-success);
}

.feature-callout--warning {
  border-left: 4px solid var(--ifm-color-warning);
  background: var(--ifm-color-emphasis-0);
}

.feature-callout--warning .feature-callout__icon {
  background: var(--ifm-color-emphasis-100);
  color: var(--ifm-color-warning);
}

.feature-callout--danger {
  border-left: 4px solid var(--ifm-color-danger);
  background: var(--ifm-color-emphasis-0);
}

.feature-callout--danger .feature-callout__icon {
  background: var(--ifm-color-emphasis-100);
  color: var(--ifm-color-danger);
}

.feature-callout--info {
  border-left: 4px solid var(--ifm-color-info);
  background: var(--ifm-color-emphasis-0);
}

.feature-callout--info .feature-callout__icon {
  background: var(--ifm-color-emphasis-100);
  color: var(--ifm-color-info);
}

/* Enhanced Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.status-indicator--small {
  font-size: 0.75rem;
  padding: 0.125rem 0.5rem;
  gap: 0.25rem;
}

.status-indicator--large {
  font-size: 1rem;
  padding: 0.5rem 1rem;
  gap: 0.75rem;
}

.status-indicator__icon {
  font-size: 0.875em;
}

.status-indicator__label {
  font-weight: 600;
}

/* Status Types */
.status-indicator--available {
  color: var(--ifm-color-success);
  background: var(--ifm-color-emphasis-0);
  border-color: var(--ifm-border-color);
}

.status-indicator--coming-soon {
  color: var(--ifm-color-warning);
  background: var(--ifm-color-emphasis-0);
  border-color: var(--ifm-border-color);
}

.status-indicator--not-available {
  color: var(--ifm-color-content-secondary);
  background: var(--ifm-color-emphasis-100);
  border-color: var(--ifm-color-emphasis-200);
}

.status-indicator--beta {
  color: var(--ifm-color-info);
  background: var(--ifm-color-emphasis-0);
  border-color: var(--ifm-border-color);
}

.status-indicator--deprecated {
  color: var(--ifm-color-danger);
  background: var(--ifm-color-emphasis-0);
  border-color: var(--ifm-border-color);
}

.status-indicator--unknown {
  color: var(--ifm-color-content-secondary);
  background: var(--ifm-color-emphasis-100);
  border-color: var(--ifm-color-emphasis-200);
}

/* ===== FINAL RESPONSIVE TOUCHES ===== */

/* Mobile optimizations for new components */
@media (max-width: 768px) {
  .api-reference__tabs {
    flex-direction: column;
  }

  .api-reference__tab {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--ifm-border-color);
    border-right: none;
  }

  .api-reference__tab--active {
    border-bottom-color: var(--ifm-border-color);
    border-left: 3px solid var(--ifm-color-primary);
  }

  .feature-callout__header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .interactive-demo__run-button {
    position: static;
    margin-top: 1rem;
    width: 100%;
    justify-content: center;
  }

  .status-indicator {
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
  }
}
