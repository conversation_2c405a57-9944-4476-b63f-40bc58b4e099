// Simple test to verify Algolia connection
const { algoliasearch } = require('algoliasearch');

const client = algoliasearch('W5X8L8ZZ8V', '********************************');

async function testSearch() {
  try {
    console.log('🔍 Testing Algolia search...');

    const results = await client.searchSingleIndex({
      indexName: 'admesh-docs',
      searchParams: {
        query: 'API',
        hitsPerPage: 5,
      },
    });

    console.log('✅ Search successful!');
    console.log(`📊 Found ${results.hits.length} results for "API"`);

    results.hits.forEach((hit, index) => {
      console.log(`${index + 1}. ${hit.title} - ${hit.url}`);
    });

  } catch (error) {
    console.error('❌ Search failed:', error.message);
  }
}

testSearch();
